package testdetails;

import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.io.FileOutputStream;
import java.time.Duration;
import java.util.List;

public class TestContent {
    private List<WebElement> totalSections; // store sections
    private WebDriver driver;
    private List<String> urls;

    public TestContent(WebDriver driver, List<String> urls) {
        this.driver = driver;
        this.urls = urls;
    }

    public void executeTests() throws Exception{
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));

            XSSFWorkbook workbook = new  XSSFWorkbook();

            for(String url :urls){
                driver.navigate().to(url);
                String sheetName = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//h2"))).getText();
                XSSFSheet sheet = workbook.createSheet(sheetName);

                SectionControl sectionControl = new SectionControl(driver,sheet);
                sectionControl.SecionExecuter();
            }

            FileOutputStream fos = new FileOutputStream("TestReport.xlsx");
            workbook.write(fos);
            fos.close();
            workbook.close();

        
    }
}
