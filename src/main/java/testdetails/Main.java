package testdetails;

import config.ConfigReader;

import utils.ExcelReader;

import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.time.Duration;
import java.util.List;

public class Main {
    public static void main(String[] args) {
        String baseurl = ConfigReader.get("baseurl");
        String username = ConfigReader.get("username");
        String password = ConfigReader.get("password");
        String filePath = ConfigReader.get("filePath");
        WebDriver driver = new ChromeDriver();
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        try{
        driver.get(baseurl);
        driver.manage().window().maximize();
        driver.switchTo().frame("loginIframe");
        driver.findElement(By.xpath("//input[@id='email']")).sendKeys(username);
        driver.findElement(By.xpath("//input[@id='password']")).sendKeys(password);
        driver.findElement(By.xpath("//button[@id='submit']")).click();

        List<String> testUrls = ExcelReader.getTestUrls(filePath);
            TestContent testContent = new  TestContent(driver,testUrls);
            testContent.executeTests();

        }
        catch (Exception e){
            System.out.println(e.getMessage());
        }
        finally{
            //driver.quit();
        }

    }
}
