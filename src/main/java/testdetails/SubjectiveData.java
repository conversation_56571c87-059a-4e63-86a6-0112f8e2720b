package testdetails;

import java.time.Duration;
import java.util.List;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

public class SubjectiveData {
    private WebDriver driver;
    private XSSFSheet sheet;
    private int rCount;

        public SubjectiveData(WebDriver driver, XSSFSheet sheet,int rCount){
        this.driver = driver;
        this.sheet = sheet;
        this.rCount  = rCount;
    }

    public void SubjectiveSolve() throws Exception{
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));

        String questionTitle = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//input[@id='txtQuesTitle']"))).getAttribute("value");
        String questionScore = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//input[@id='score']"))).getAttribute("value");
        String questionKeywords="";
        String questionDescription = "";

        try{
            List<WebElement> tags = wait.until(ExpectedConditions.presenceOfAllElementsLocatedBy(
                By.xpath("//div[@class='ant-select-selection-overflow']//span/span[@class='ant-tag ant-tag-default css-f9u17k']")));
            
            for(WebElement tag: tags){
                questionKeywords += tag.getText()+" ";
            }

            questionDescription = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//div[@id=\"txtQues\"]//div[@class=\"ql-editor\"]/p"))).getText();

        }catch(Exception e){
            System.out.println("No keywords or descriptions");
        }
        
        Row row = sheet.createRow(rCount);
        row.createCell(0).setCellValue("Subjective");
        row.createCell(1).setCellValue(questionTitle);
        row.createCell(2).setCellValue(questionDescription);
        row.createCell(3).setCellValue(questionScore);
        row.createCell(4).setCellValue(questionKeywords);

        driver.switchTo().defaultContent();
        driver.close();
    }

}
