package testdetails;

import java.time.Duration;
import java.util.List;

import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedCondition;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;


public class SectionControl {

    private WebDriver driver;
    private XSSFSheet sheet;
    private int rCount =1;

    public SectionControl(WebDriver driver, XSSFSheet sheet){
        this.driver = driver;
        this.sheet = sheet;
    }

    public void SecionExecuter()throws Exception{
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));

        List<WebElement> totalSections = wait.until(ExpectedConditions.presenceOfAllElementsLocatedBy(By.xpath("//tbody/tr/td[2]//div[@role='button']")));

        //sheet header
        createHeader();

        for(int i=0;i<totalSections.size();i++){
            wait.until(ExpectedConditions.presenceOfAllElementsLocatedBy(By.xpath("//tbody/tr/td[2]//div[@role='button']"))).get(i).click();
            QuestionInvigilator questionInvigilator = new QuestionInvigilator(driver,sheet, i+1);
            
            rCount =questionInvigilator.invigilateQuestion(rCount);
        }
    }
        private void createHeader() {
        org.apache.poi.ss.usermodel.Row headerRow = sheet.createRow(0);

        headerRow.createCell(0).setCellValue("Question Type");
        headerRow.createCell(1).setCellValue("Question Name");
        headerRow.createCell(2).setCellValue("Question Description");
        headerRow.createCell(3).setCellValue("Question Score");
        headerRow.createCell(4).setCellValue("Question Keywords");
        headerRow.createCell(5).setCellValue("Allowed languages");
        headerRow.createCell(6).setCellValue("MCQ Options");
        headerRow.createCell(7).setCellValue("Web TestCases");
        headerRow.createCell(8).setCellValue("MQ Questions");

    }
}
