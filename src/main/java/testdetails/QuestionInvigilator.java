package testdetails;

import java.rmi.server.ExportException;
import java.time.Duration;
import java.util.List;
import java.util.Set;

import org.apache.poi.ss.formula.atp.Switch;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

public class QuestionInvigilator {
    private WebDriver driver;
    private XSSFSheet sheet;
    private int sectionIndex;

    public QuestionInvigilator(WebDriver driver, XSSFSheet sheet, int sectionIndex) {
        this.driver = driver;
        this.sheet = sheet;
        this.sectionIndex = sectionIndex;
    }

    public int invigilateQuestion(int rCount) throws Exception {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        String originalTab = driver.getWindowHandle();

        // Question count for each section
        List<WebElement> questions = wait.until(ExpectedConditions
                .presenceOfAllElementsLocatedBy(By.xpath("//tbody/tr[" + sectionIndex + "]//table/tbody/tr")));

        for (int i = 0; i < questions.size(); i++) {
            // select question one by one
            WebElement question = wait.until(ExpectedConditions.presenceOfElementLocated(
                    By.xpath("//tbody/tr[" + sectionIndex + "]//table/tbody/tr[" + (i + 1) + "]")));

            wait.until(ExpectedConditions.visibilityOf(question));
            String questionType = question.findElement(By.xpath(".//td[5]")).getText();

            WebElement editButton;
            if (questionType.equals("Coding")) {
                editButton = question.findElement(By.xpath("(.//td[8]//button)[2]"));
            } else {
                editButton = question.findElement(By.xpath(".//td[8]//button"));
            }
            wait.until(ExpectedConditions.visibilityOf(editButton));
            wait.until(ExpectedConditions.elementToBeClickable(editButton));
            editButton.click();

            Set<String> windows = driver.getWindowHandles();

            String questionTab = "";
            for (String tab : windows) {
                if (!tab.equals(originalTab)) {
                    questionTab = tab;
                }
            }

            // switch to question tab
            if (!questionTab.equals("")) {
                driver.switchTo().window(questionTab);
            }

            switch (questionType) {
                case "Coding":
                    CodingData coding = new CodingData(driver, sheet, rCount);
                    coding.codingSolve();
                    rCount++;
                    break;

                case "MCQ":
                    MCQ mcq = new MCQ(driver, sheet, rCount);
                    mcq.mcqSolve();
                    rCount++;
                    break;

                case "Subjective":
                    SubjectiveData subjectiveData = new SubjectiveData(driver, sheet, rCount);
                    subjectiveData.SubjectiveSolve();
                    rCount++;
                    break;

                case "Web":
                    WebData webData = new WebData(driver, sheet, rCount);
                    webData.webSolve();
                    rCount++;
                    break;

                case "MQ":
                    MQData mqData = new MQData(driver, sheet, rCount);
                    mqData.mqSolve();
                    rCount++;
                    break;

                default:
                    System.out.println("unknown question type: " + questionType);
            }

            driver.switchTo().window(originalTab);

        }
        return rCount;
    }

}
